# Google Chat Webhook URL for notifications
GOO<PERSON><PERSON>_WEBHOOK_URL=your_google_chat_webhook_url_here

# Google Sheets Configuration
# Get these from Google Cloud Console after setting up Google Sheets API
GOOGLE_SPREADSHEET_ID=your_google_spreadsheet_id_here
GOOGLE_CREDENTIALS_FILE=credentials.json
GOOGLE_TOKEN_FILE=token.json

# Instructions for Google Sheets Setup:
# 1. Go to Google Cloud Console (https://console.cloud.google.com/)
# 2. Create a new project or select existing one
# 3. Enable Google Sheets API
# 4. Create credentials (OAuth 2.0 Client ID for Desktop Application)
# 5. Download the credentials.json file and place it in the project root
# 6. Create a Google Spreadsheet and copy its ID from the URL
# 7. Set GOOGLE_SPREADSHEET_ID to your spreadsheet ID
# 8. Run the script - it will prompt for authorization on first run
