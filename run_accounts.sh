#!/bin/bash

# Script để chạy tự động worker.py với JSON-based account management
# Chạy liên tục cho đến khi tất cả tài khoản có status "success"

# Kiểm tra xem file accounts.json có tồn tại không
if [ ! -f "accounts.json" ]; then
    echo "Error: File accounts.json không tồn tại!"
    exit 1
fi

# Kiểm tra xem file worker.py có tồn tại không
if [ ! -f "worker.py" ]; then
    echo "Error: File worker.py không tồn tại!"
    exit 1
fi

# Kiểm tra xem jq có được cài đặt không (để parse JSON)
if ! command -v jq &> /dev/null; then
    echo "Error: jq chưa được cài đặt. Vui lòng cài đặt jq để parse JSON."
    echo "Ubuntu/Debian: sudo apt-get install jq"
    echo "CentOS/RHEL: sudo yum install jq"
    echo "macOS: brew install jq"
    exit 1
fi

# Function để kiểm tra xem còn tài khoản nào có status "processing" không
check_processing_accounts() {
    local processing_count=$(jq '[.[] | select(.status == "processing")] | length' accounts.json)
    echo $processing_count
}

# Function để hiển thị trạng thái tài khoản
show_account_status() {
    echo "=== TRẠNG THÁI TÀI KHOẢN ==="
    local total_count=$(jq length accounts.json)
    local success_count=$(jq '[.[] | select(.status == "success")] | length' accounts.json)
    local processing_count=$(jq '[.[] | select(.status == "processing")] | length' accounts.json)
    local untried_count=$(jq '[.[] | select(.status == "processing" and has("last_tried") | not)] | length' accounts.json)
    local tried_failed_count=$(jq '[.[] | select(.status == "processing" and has("last_tried"))] | length' accounts.json)

    echo "Tổng số tài khoản: $total_count"
    echo "Đã hoàn thành: $success_count"
    echo "Đang chờ xử lý: $processing_count"
    echo "  - Chưa thử: $untried_count"
    echo "  - Lỗi (sẽ thử lại): $tried_failed_count"
    echo "=========================="
}

echo "Bắt đầu chạy script với JSON-based account management..."
echo "Thời gian bắt đầu: $(date '+%Y-%m-%d %H:%M:%S')"
show_account_status

# Vòng lặp chính - chạy cho đến khi tất cả tài khoản có status "success"
round=1
while [ $(check_processing_accounts) -gt 0 ]; do
    echo ""
    echo "=== VÒNG $round ==="
    echo "Thời gian: $(date '+%Y-%m-%d %H:%M:%S')"

    # Chạy worker.py với chế độ JSON (single-execution mode)
    echo "Đang chạy worker.py --json..."
    python worker.py --json

    # Kiểm tra kết quả
    if [ $? -eq 0 ]; then
        echo "✓ Worker.py hoàn thành thành công"
    else
        echo "✗ Worker.py gặp lỗi"
    fi

    # Hiển thị trạng thái hiện tại
    show_account_status

    # Kiểm tra xem còn tài khoản nào cần xử lý không
    remaining=$(check_processing_accounts)
    if [ $remaining -gt 0 ]; then
        echo "Còn $remaining tài khoản cần xử lý. Chờ 2 phút trước khi tiếp tục..."
        sleep 120
    fi

    round=$((round + 1))
done

echo ""
echo "🎉 TẤT CẢ TÀI KHOẢN ĐÃ ĐƯỢC XỬ LÝ THÀNH CÔNG!"
echo "Thời gian hoàn thành: $(date '+%Y-%m-%d %H:%M:%S')"
show_account_status

echo "================================"
echo "Hoàn thành tất cả tài khoản!"
echo "Thời gian kết thúc: $(date '+%Y-%m-%d %H:%M:%S')"
