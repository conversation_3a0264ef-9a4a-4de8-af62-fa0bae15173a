import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional
import json

from openpyxl import Workbook, load_workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from loguru import logger


class ExcelLogger:
    def __init__(self, filename: str = "rewards_log.xlsx"):
        self.filename = filename
        self.workbook = None
        self.worksheet = None
        self._initialize_workbook()

    def _initialize_workbook(self):
        """Initialize or load existing workbook"""
        try:
            if os.path.exists(self.filename):
                self.workbook = load_workbook(self.filename)
                self.worksheet = self.workbook.active
                logger.info(f"Loaded existing Excel file: {self.filename}")
            else:
                self.workbook = Workbook()
                self.worksheet = self.workbook.active
                self.worksheet.title = "Rewards Log"
                self._create_headers()
                logger.info(f"Created new Excel file: {self.filename}")
        except Exception as e:
            logger.error(f"Error initializing Excel file: {e}")

    def _create_headers(self):
        """Create headers in the Excel file"""
        yesterday, today = self._get_yesterday_today_dates()
        
        headers = [
            'Email',
            f'Previous Day ({self._get_date_string(yesterday)})',
            f'Current Day ({self._get_date_string(today)})',
            'Points Earned Today',
            'Last Updated',
            'Status'
        ]
        
        # Set headers
        for col, header in enumerate(headers, 1):
            cell = self.worksheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # Auto-adjust column widths
        column_widths = [30, 15, 15, 18, 20, 12]
        for col, width in enumerate(column_widths, 1):
            self.worksheet.column_dimensions[chr(64 + col)].width = width

    def _get_date_string(self, date: datetime) -> str:
        """Convert datetime to string format"""
        return date.strftime('%Y-%m-%d')

    def _get_yesterday_today_dates(self) -> tuple:
        """Get yesterday and today dates"""
        today = datetime.now()
        yesterday = today - timedelta(days=1)
        return yesterday, today

    def _find_account_row(self, email: str) -> int:
        """Find existing row for account or return next available row. Returns row number (1-based)"""
        if not self.worksheet:
            return -1

        # Check if we need to update headers for new day
        self._update_headers_if_needed()

        # Find existing account
        for row in range(2, self.worksheet.max_row + 1):
            if self.worksheet.cell(row=row, column=1).value == email:
                return row
        
        # Account not found, return next available row
        return self.worksheet.max_row + 1

    def _update_headers_if_needed(self):
        """Update headers if the date has changed"""
        if not self.worksheet:
            return

        try:
            # Check current headers
            current_prev_day = self.worksheet.cell(row=1, column=2).value
            current_curr_day = self.worksheet.cell(row=1, column=3).value
            
            yesterday, today = self._get_yesterday_today_dates()
            expected_prev_day = f'Previous Day ({self._get_date_string(yesterday)})'
            expected_curr_day = f'Current Day ({self._get_date_string(today)})'
            
            # Check if headers need updating (date changed)
            if (current_prev_day != expected_prev_day or 
                current_curr_day != expected_curr_day):
                
                # Shift data: current day becomes previous day
                self._shift_data_for_new_day()
                
                # Update headers
                self.worksheet.cell(row=1, column=2, value=expected_prev_day)
                self.worksheet.cell(row=1, column=3, value=expected_curr_day)
                
                logger.info("Updated Excel headers for new day")

        except Exception as e:
            logger.error(f"Error updating headers: {e}")

    def _shift_data_for_new_day(self):
        """Shift current day data to previous day column for all accounts"""
        if not self.worksheet:
            return

        try:
            # Shift data for all accounts
            for row in range(2, self.worksheet.max_row + 1):
                current_day_value = self.worksheet.cell(row=row, column=3).value
                if current_day_value is not None:
                    # Move current day (column 3) to previous day (column 2)
                    self.worksheet.cell(row=row, column=2, value=current_day_value)
                    # Clear current day (column 3) and points earned (column 4)
                    self.worksheet.cell(row=row, column=3, value="")
                    self.worksheet.cell(row=row, column=4, value="")
            
            logger.info("Shifted Excel data for new day")

        except Exception as e:
            logger.error(f"Error shifting data: {e}")

    def log_account_data(self, email: str, current_points: int, previous_points: int = None, status: str = "success"):
        """
        Log account data to Excel file
        
        Args:
            email: Account email
            current_points: Current day's reward points
            previous_points: Previous day's reward points (optional)
            status: Account status
        """
        if not self.worksheet:
            logger.error("Excel worksheet not available")
            return False

        try:
            # Find or create row for this account
            row_num = self._find_account_row(email)
            if row_num == -1:
                logger.error(f"Failed to find/create row for account: {email}")
                return False

            # Get existing previous points if not provided
            if previous_points is None:
                existing_prev = self.worksheet.cell(row=row_num, column=2).value
                previous_points = int(existing_prev) if existing_prev and str(existing_prev).isdigit() else 0
            
            # Calculate points earned
            points_earned = current_points - previous_points
            last_updated = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Update row data
            self.worksheet.cell(row=row_num, column=1, value=email)  # A: Email
            self.worksheet.cell(row=row_num, column=2, value=previous_points)  # B: Previous day points
            self.worksheet.cell(row=row_num, column=3, value=current_points)  # C: Current day points
            self.worksheet.cell(row=row_num, column=4, value=points_earned)  # D: Points earned today
            self.worksheet.cell(row=row_num, column=5, value=last_updated)  # E: Last updated
            self.worksheet.cell(row=row_num, column=6, value=status)  # F: Status
            
            # Apply formatting for new rows
            if row_num > self.worksheet.max_row - 1:
                for col in range(1, 7):
                    cell = self.worksheet.cell(row=row_num, column=col)
                    cell.alignment = Alignment(horizontal="center" if col in [2, 3, 4, 6] else "left")
            
            # Color code based on status
            status_cell = self.worksheet.cell(row=row_num, column=6)
            if status == "success":
                status_cell.fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
            elif status == "processing":
                status_cell.fill = PatternFill(start_color="FFE4B5", end_color="FFE4B5", fill_type="solid")
            else:
                status_cell.fill = PatternFill(start_color="FFB6C1", end_color="FFB6C1", fill_type="solid")

            # Save the workbook
            self.workbook.save(self.filename)
            
            logger.info(f"Successfully logged {email} to Excel: {previous_points} -> {current_points} (+{points_earned})")
            return True

        except Exception as e:
            logger.error(f"Error logging account data to Excel: {e}")
            return False

    def get_account_data(self, email: str) -> Optional[Dict]:
        """Get existing account data from Excel"""
        if not self.worksheet:
            return None

        try:
            for row in range(2, self.worksheet.max_row + 1):
                if self.worksheet.cell(row=row, column=1).value == email:
                    return {
                        'email': email,
                        'previous_points': self.worksheet.cell(row=row, column=2).value or 0,
                        'current_points': self.worksheet.cell(row=row, column=3).value or 0,
                        'points_earned': self.worksheet.cell(row=row, column=4).value or 0,
                        'last_updated': self.worksheet.cell(row=row, column=5).value,
                        'status': self.worksheet.cell(row=row, column=6).value
                    }
            return None
        except Exception as e:
            logger.error(f"Error getting account data: {e}")
            return None


# Global instance
_excel_logger = None

def get_excel_logger() -> ExcelLogger:
    """Get or create Excel logger instance"""
    global _excel_logger
    if _excel_logger is None:
        _excel_logger = ExcelLogger()
    return _excel_logger


def log_account_to_excel(email: str, current_points: int, previous_points: int = None, status: str = "success") -> bool:
    """
    Convenience function to log account data to Excel
    
    Args:
        email: Account email
        current_points: Current day's reward points
        previous_points: Previous day's reward points (optional)
        status: Account status
    
    Returns:
        bool: True if successful, False otherwise
    """
    logger_instance = get_excel_logger()
    return logger_instance.log_account_data(email, current_points, previous_points, status)
