import json
import subprocess
import time
import schedule
import argparse

from loguru import logger

from src.util.gg_chat_logging import post_message

ACCOUNT_CONFIG_PATH = 'account.json'

def read_config():
    with open(ACCOUNT_CONFIG_PATH, 'r') as f:
        return json.load(f)


def sync_gg_sheet(result: dict):
    logger.info("Start syncing google sheet...")
    logger.info("Sync google sheet finished")


def run_script(email: str, password: str):
    logger.info(f"Start running script with '{email}'...")
    point = "__"
    status = "failed"
    try:
        proc = subprocess.run(
            ["python", "src/main.py", "--email", email, "--password", password],
            check=True,
            capture_output=True,
            text=True,
        )

        lines = proc.stdout.splitlines()
        for line in lines:
            print(line)
            if "===RESULT===" in line:
                data_str = line.split("===RESULT===: ")[1].split("}")[0] + "}"
                result = json.loads(data_str)
                point = result['point']
                status = "success"
    except subprocess.CalledProcessError:
        point = "__"
        status = "failed"
        logger.error(f"Failed to run script with '{email}'")
    return {
        "email": email,
        "password": password,
        "point": point,
        "status": status
    }


def point_farm():
    logger.info("Start point farming...")
    accounts = read_config()
    for _ in range(10):
        for i, acc in enumerate(accounts):
            if not 'status' in acc or acc['status'] == "failed":
                result = run_script(acc['email'], acc['password'])
                accounts[i] = result
                sync_gg_sheet(result)
                time.sleep(90)
    logger.info("Point farm finished")
    post_message(f"Result {time.strftime('%d-%m-%Y %H:%M:%S')}:\n{json.dumps(accounts, indent=4)}")


def run_single_account(email: str, password: str):
    """Run script for a single account directly"""
    logger.info(f"Running single account mode for {email}")
    result = run_script(email, password)
    logger.info(f"Result: {result}")
    return result


def run_from_json():
    """Run script for all accounts from accounts.json file"""
    logger.info("Running from accounts.json file...")
    try:
        with open('accounts.json', 'r') as f:
            accounts = json.load(f)

        results = []
        for i, account in enumerate(accounts):
            email = account.get('email')
            password = account.get('password')

            if email and password:
                logger.info(f"[{i+1}/{len(accounts)}] Processing account: {email}")
                result = run_script(email, password)
                results.append(result)

                # Wait 2 minutes between accounts (except for the last one)
                if i < len(accounts) - 1:
                    logger.info("Waiting 2 minutes before next account...")
                    time.sleep(120)
            else:
                logger.warning(f"Skipping account {i+1}: missing email or password")

        logger.info("All accounts processed!")
        return results

    except FileNotFoundError:
        logger.error("accounts.json file not found!")
        return []
    except json.JSONDecodeError:
        logger.error("Invalid JSON format in accounts.json!")
        return []


def main():
    parser = argparse.ArgumentParser(description='Run edge rewards script')
    parser.add_argument('--email', type=str, help='Email address for single account')
    parser.add_argument('--password', type=str, help='Password for single account')
    parser.add_argument('--json', action='store_true', help='Run all accounts from accounts.json')
    parser.add_argument('--scheduler', action='store_true', help='Run as scheduler')

    args = parser.parse_args()

    if args.email and args.password:
        # Run single account mode
        run_single_account(args.email, args.password)
    elif args.json:
        # Run from JSON file
        run_from_json()
    elif args.scheduler:
        # Run scheduler mode
        schedule.every().day.at("01:18").do(point_farm)
        logger.info("Start worker scheduler...")
        while True:
            schedule.run_pending()
            time.sleep(1)
    else:
        # Default: run point farm once
        logger.info("Running point farm once...")
        point_farm()


if __name__ == "__main__":
    main()
