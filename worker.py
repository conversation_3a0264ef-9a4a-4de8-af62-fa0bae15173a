import json
import subprocess
import time
import schedule
import argparse

from loguru import logger

from src.util.gg_chat_logging import post_message
from src.util.google_sheets import sync_account_to_sheets

ACCOUNT_CONFIG_PATH = 'account.json'

def read_config():
    with open(ACCOUNT_CONFIG_PATH, 'r') as f:
        return json.load(f)


def sync_gg_sheet(result: dict):
    """
    Sync account data to Google Sheets

    Args:
        result: Dictionary containing account data with keys:
                - email: Account email
                - point: Current reward points
                - status: Account status
    """
    logger.info("Start syncing google sheet...")

    try:
        email = result.get('email')
        current_points = result.get('point', 0)

        if not email:
            logger.error("No email found in result data")
            return

        # Convert points to integer if it's a string
        if isinstance(current_points, str):
            if current_points.isdigit():
                current_points = int(current_points)
            else:
                logger.warning(f"Invalid points value: {current_points}, setting to 0")
                current_points = 0

        # Get previous points from accounts.json
        previous_points = None
        try:
            with open('accounts.json', 'r') as f:
                accounts = json.load(f)

            for account in accounts:
                if account.get('email') == email:
                    previous_points = account.get('reward', 0)
                    break
        except Exception as e:
            logger.warning(f"Could not read previous points from accounts.json: {e}")

        # Sync to Google Sheets
        success = sync_account_to_sheets(
            email=email,
            current_points=current_points,
            previous_points=previous_points
        )

        if success:
            logger.info(f"Successfully synced {email} to Google Sheets")
        else:
            logger.warning(f"Failed to sync {email} to Google Sheets")

    except Exception as e:
        logger.error(f"Error in sync_gg_sheet: {e}")

    logger.info("Sync google sheet finished")


def run_script(email: str, password: str):
    logger.info(f"Start running script with '{email}'...")
    point = "__"
    status = "failed"
    try:
        proc = subprocess.run(
            ["python", "src/main.py", "--email", email, "--password", password],
            check=True,
            capture_output=True,
            text=True,
        )

        lines = proc.stdout.splitlines()
        for line in lines:
            print(line)
            if "===RESULT===" in line:
                data_str = line.split("===RESULT===: ")[1].split("}")[0] + "}"
                result = json.loads(data_str)
                point = result['point']
                status = "success"
    except subprocess.CalledProcessError:
        point = "__"
        status = "failed"
        logger.error(f"Failed to run script with '{email}'")
    return {
        "email": email,
        "password": password,
        "point": point,
        "status": status
    }


def point_farm():
    logger.info("Start point farming...")
    accounts = read_config()
    for _ in range(10):
        for i, acc in enumerate(accounts):
            if not 'status' in acc or acc['status'] == "failed":
                result = run_script(acc['email'], acc['password'])
                accounts[i] = result
                sync_gg_sheet(result)
                time.sleep(90)
    logger.info("Point farm finished")
    post_message(f"Result {time.strftime('%d-%m-%Y %H:%M:%S')}:\n{json.dumps(accounts, indent=4)}")


def run_single_account(email: str, password: str):
    """Run script for a single account directly"""
    logger.info(f"Running single account mode for {email}")
    result = run_script(email, password)
    logger.info(f"Result: {result}")
    return result


def update_account_status(email: str, status: str, reward: int = None):
    """Update account status in accounts.json file"""
    try:
        with open('accounts.json', 'r') as f:
            accounts = json.load(f)

        for account in accounts:
            if account.get('email') == email:
                account['status'] = status
                if reward is not None:
                    account['reward'] = reward
                break

        with open('accounts.json', 'w') as f:
            json.dump(accounts, f, indent=2)

        logger.info(f"Updated account {email} status to {status}")

    except Exception as e:
        logger.error(f"Failed to update account status: {e}")


def get_next_processing_account():
    """Get the next account with 'processing' status"""
    try:
        with open('accounts.json', 'r') as f:
            accounts = json.load(f)

        for account in accounts:
            if account.get('status') == 'processing':
                return account

        logger.info("No accounts with 'processing' status found")
        return None

    except FileNotFoundError:
        logger.error("accounts.json file not found!")
        return None
    except json.JSONDecodeError:
        logger.error("Invalid JSON format in accounts.json!")
        return None


def run_single_account_from_json():
    """Run script for a single account from accounts.json file (single-execution mode)"""
    logger.info("Running single account from accounts.json file...")

    account = get_next_processing_account()
    if not account:
        logger.info("No accounts to process")
        return None

    email = account.get('email')
    password = account.get('password')

    if not email or not password:
        logger.error(f"Missing email or password for account: {email}")
        return None

    logger.info(f"Processing account: {email}")
    result = run_script(email, password)

    # Update account status based on result
    if result.get('status') == 'success':
        # Extract reward points from result
        reward_points = result.get('point', 0)
        try:
            # Convert to integer if it's a string
            if isinstance(reward_points, str) and reward_points.isdigit():
                reward_points = int(reward_points)
            elif isinstance(reward_points, str):
                reward_points = 0  # If it's not a valid number, set to 0
        except:
            reward_points = 0

        update_account_status(email, 'success', reward_points)
        sync_gg_sheet(result)
    else:
        logger.warning(f"Account {email} processing failed, keeping status as 'processing'")

    return result


def main():
    parser = argparse.ArgumentParser(description='Run edge rewards script')
    parser.add_argument('--email', type=str, help='Email address for single account')
    parser.add_argument('--password', type=str, help='Password for single account')
    parser.add_argument('--json', action='store_true', help='Run all accounts from accounts.json')
    parser.add_argument('--scheduler', action='store_true', help='Run as scheduler')

    args = parser.parse_args()

    if args.email and args.password:
        # Run single account mode
        run_single_account(args.email, args.password)
    elif args.json:
        # Run single account from JSON file (single-execution mode)
        run_single_account_from_json()
    elif args.scheduler:
        # Run scheduler mode
        schedule.every().day.at("01:18").do(point_farm)
        logger.info("Start worker scheduler...")
        while True:
            schedule.run_pending()
            time.sleep(1)
    else:
        # Default: run single account from JSON file
        logger.info("Running single account from JSON file (default mode)...")
        run_single_account_from_json()


if __name__ == "__main__":
    main()
