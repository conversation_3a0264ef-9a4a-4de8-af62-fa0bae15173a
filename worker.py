import json
import subprocess
import time
import schedule
import argparse

from loguru import logger

from src.util.gg_chat_logging import post_message
from src.util.excel_logger import log_account_to_excel


def read_accounts_json_safe():
    """Safely read accounts.json with multiple encoding attempts"""
    try:
        # Try different encodings to handle manual edits
        encodings = ['utf-8', 'utf-8-sig', 'ascii', 'latin-1', 'cp1252']

        for encoding in encodings:
            try:
                with open('accounts.json', 'r', encoding=encoding) as f:
                    content = f.read().strip()
                    # Remove BOM if present
                    if content.startswith('\ufeff'):
                        content = content[1:]
                    # Remove any trailing commas before closing brackets (common manual edit error)
                    content = content.replace(',\n]', '\n]').replace(',]', ']')
                    accounts = json.loads(content)
                    logger.debug(f"Successfully read accounts.json with {encoding} encoding")
                    return accounts
            except (UnicodeDecodeError, json.JSONDecodeError) as e:
                logger.debug(f"Failed to read with {encoding}: {e}")
                continue

        logger.error("Could not read accounts.json with any encoding!")
        return None

    except FileNotFoundError:
        logger.error("accounts.json file not found!")
        return None
    except Exception as e:
        logger.error(f"Unexpected error reading accounts.json: {e}")
        return None


def write_accounts_json_safe(accounts):
    """Safely write accounts.json with proper encoding"""
    try:
        # Always write with UTF-8 encoding for consistency
        with open('accounts.json', 'w', encoding='utf-8') as f:
            json.dump(accounts, f, indent=2, ensure_ascii=False)
        logger.debug("Successfully wrote accounts.json")
        return True
    except Exception as e:
        logger.error(f"Failed to write accounts.json: {e}")
        return False


def sync_excel_log(result: dict):
    """
    Sync account data to Excel file

    Args:
        result: Dictionary containing account data with keys:
                - email: Account email
                - point: Current reward points
                - status: Account status
    """
    logger.info("Start syncing to Excel file...")

    try:
        email = result.get('email')
        current_points = result.get('point', 0)
        status = result.get('status', 'success')

        if not email:
            logger.error("No email found in result data")
            return

        # Convert points to integer if it's a string
        if isinstance(current_points, str):
            if current_points.isdigit():
                current_points = int(current_points)
            else:
                logger.warning(f"Invalid points value: {current_points}, setting to 0")
                current_points = 0

        # Get previous points from accounts.json
        previous_points = None
        try:
            accounts = read_accounts_json_safe()
            if accounts:
                for account in accounts:
                    if account.get('email') == email:
                        previous_points = account.get('reward', 0)
                        break
        except Exception as e:
            logger.warning(f"Could not read previous points from accounts.json: {e}")

        # Log to Excel file
        success = log_account_to_excel(
            email=email,
            current_points=current_points,
            previous_points=previous_points,
            status=status
        )

        if success:
            logger.info(f"Successfully logged {email} to Excel file")
        else:
            logger.warning(f"Failed to log {email} to Excel file")

    except Exception as e:
        logger.error(f"Error in sync_excel_log: {e}")

    logger.info("Excel sync finished")


def run_script(email: str, password: str):
    logger.info(f"Start running script with '{email}'...")
    point = "__"
    status = "failed"
    try:
        proc = subprocess.run(
            ["python", "src/main.py", "--email", email, "--password", password],
            check=True,
            capture_output=True,
            text=True,
        )

        lines = proc.stdout.splitlines()
        for line in lines:
            print(line)
            if "===RESULT===" in line:
                data_str = line.split("===RESULT===: ")[1].split("}")[0] + "}"
                result = json.loads(data_str)
                point = result['point']
                status = "success"
    except subprocess.CalledProcessError:
        point = "__"
        status = "failed"
        logger.error(f"Failed to run script with '{email}'")
    return {
        "email": email,
        "password": password,
        "point": point,
        "status": status
    }


def point_farm():
    logger.info("Start point farming...")
    accounts = read_accounts_json_safe()
    if not accounts:
        logger.error("Could not read accounts for point farming!")
        return

    for _ in range(10):
        for i, acc in enumerate(accounts):
            if not 'status' in acc or acc['status'] == "failed":
                result = run_script(acc['email'], acc['password'])
                accounts[i] = result
                sync_excel_log(result)
                time.sleep(90)
    logger.info("Point farm finished")
    post_message(f"Result {time.strftime('%d-%m-%Y %H:%M:%S')}:\n{json.dumps(accounts, indent=4)}")


def run_single_account(email: str, password: str):
    """Run script for a single account directly"""
    logger.info(f"Running single account mode for {email}")
    result = run_script(email, password)
    logger.info(f"Result: {result}")
    return result


def update_account_status(email: str, status: str, reward: int = None):
    """Update account status in accounts.json file"""
    try:
        accounts = read_accounts_json_safe()
        if accounts is None:
            logger.error("Could not read accounts.json for updating!")
            return

        # Update account
        for account in accounts:
            if account.get('email') == email:
                account['status'] = status
                if reward is not None:
                    account['reward'] = reward
                break

        # Write back to file
        if write_accounts_json_safe(accounts):
            logger.info(f"Updated account {email} status to {status}")
        else:
            logger.error(f"Failed to write updated status for {email}")

    except Exception as e:
        logger.error(f"Failed to update account status: {e}")


def mark_account_as_tried(email: str):
    """Mark account as tried by adding timestamp"""
    try:
        from datetime import datetime

        accounts = read_accounts_json_safe()
        if accounts is None:
            logger.error("Could not read accounts.json for marking as tried!")
            return

        # Mark account as tried
        for account in accounts:
            if account.get('email') == email:
                account['last_tried'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                break

        # Write back to file
        if write_accounts_json_safe(accounts):
            logger.debug(f"Marked account {email} as tried")
        else:
            logger.error(f"Failed to mark {email} as tried")

    except Exception as e:
        logger.error(f"Failed to mark account as tried: {e}")


def get_next_processing_account():
    """Get the next account with 'processing' status, prioritizing untried accounts"""
    try:
        accounts = read_accounts_json_safe()
        if accounts is None:
            return None

        # Calculate statistics and collect accounts
        total = len(accounts)
        success = sum(1 for acc in accounts if acc.get('status') == 'success')
        untried_accounts = []
        tried_accounts = []

        for account in accounts:
            if account.get('status') == 'processing':
                if 'last_tried' not in account:
                    untried_accounts.append(account)
                else:
                    tried_accounts.append(account)

        # Show statistics
        logger.info(f"📊 Account Status: {success}/{total} success, "
                   f"{len(untried_accounts)} untried, {len(tried_accounts)} failed (will retry)")

        # First priority: untried accounts (in order)
        if untried_accounts:
            account = untried_accounts[0]  # Take first untried account
            logger.info(f"🆕 Found untried account: {account['email']}")
            return account

        # Second priority: previously tried accounts (in order)
        if tried_accounts:
            account = tried_accounts[0]  # Take first tried account
            logger.info(f"🔄 Found previously tried account: {account['email']} (retry)")
            return account

        logger.info("✅ No accounts with 'processing' status found - all completed!")
        return None

    except Exception as e:
        logger.error(f"Error getting next processing account: {e}")
        return None


def run_single_account_from_json():
    """Run script for a single account from accounts.json file (single-execution mode)"""
    logger.info("Running single account from accounts.json file...")

    account = get_next_processing_account()
    if not account:
        logger.info("No accounts to process")
        return None

    email = account.get('email')
    password = account.get('password')

    if not email or not password:
        logger.error(f"Missing email or password for account: {email}")
        # Mark as tried even if missing credentials
        mark_account_as_tried(email)
        return None

    logger.info(f"Processing account: {email}")

    # Mark account as tried before processing
    mark_account_as_tried(email)

    result = run_script(email, password)

    # Update account status based on result
    if result.get('status') == 'success':
        # Extract reward points from result
        reward_points = result.get('point', 0)
        try:
            # Convert to integer if it's a string
            if isinstance(reward_points, str) and reward_points.isdigit():
                reward_points = int(reward_points)
            elif isinstance(reward_points, str):
                reward_points = 0  # If it's not a valid number, set to 0
        except:
            reward_points = 0

        update_account_status(email, 'success', reward_points)
        sync_excel_log(result)
        logger.info(f"✅ Account {email} completed successfully")
    else:
        logger.warning(f"❌ Account {email} processing failed, keeping status as 'processing' for retry later")

    return result


def main():
    parser = argparse.ArgumentParser(description='Run edge rewards script')
    parser.add_argument('--email', type=str, help='Email address for single account')
    parser.add_argument('--password', type=str, help='Password for single account')
    parser.add_argument('--json', action='store_true', help='Run all accounts from accounts.json')
    parser.add_argument('--scheduler', action='store_true', help='Run as scheduler')

    args = parser.parse_args()

    if args.email and args.password:
        # Run single account mode
        run_single_account(args.email, args.password)
    elif args.json:
        # Run single account from JSON file (single-execution mode)
        run_single_account_from_json()
    elif args.scheduler:
        # Run scheduler mode
        schedule.every().day.at("01:18").do(point_farm)
        logger.info("Start worker scheduler...")
        while True:
            schedule.run_pending()
            time.sleep(1)
    else:
        # Default: run single account from JSON file
        logger.info("Running single account from JSON file (default mode)...")
        run_single_account_from_json()


if __name__ == "__main__":
    main()
