import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from loguru import logger
from dotenv import load_dotenv

load_dotenv()

# If modifying these scopes, delete the file token.json.
SCOPES = ['https://www.googleapis.com/auth/spreadsheets']

# Google Sheets configuration from environment variables
SPREADSHEET_ID = os.getenv('GOOGLE_SPREADSHEET_ID')
CREDENTIALS_FILE = os.getenv('GOOGLE_CREDENTIALS_FILE', 'credentials.json')
TOKEN_FILE = os.getenv('GOOGLE_TOKEN_FILE', 'token.json')


class GoogleSheetsManager:
    def __init__(self):
        self.service = None
        self.spreadsheet_id = SPREADSHEET_ID
        self._authenticate()

    def _authenticate(self):
        """Authenticate with Google Sheets API"""
        creds = None
        
        # The file token.json stores the user's access and refresh tokens.
        if os.path.exists(TOKEN_FILE):
            creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)
        
        # If there are no (valid) credentials available, let the user log in.
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                if not os.path.exists(CREDENTIALS_FILE):
                    logger.error(f"Google credentials file not found: {CREDENTIALS_FILE}")
                    logger.error("Please download credentials.json from Google Cloud Console")
                    return
                
                flow = InstalledAppFlow.from_client_secrets_file(CREDENTIALS_FILE, SCOPES)
                creds = flow.run_local_server(port=0)
            
            # Save the credentials for the next run
            with open(TOKEN_FILE, 'w') as token:
                token.write(creds.to_json())

        try:
            self.service = build('sheets', 'v4', credentials=creds)
            logger.info("Successfully authenticated with Google Sheets API")
        except Exception as e:
            logger.error(f"Failed to authenticate with Google Sheets API: {e}")

    def _get_date_string(self, date: datetime) -> str:
        """Convert datetime to string format for sheets"""
        return date.strftime('%Y-%m-%d')

    def _get_yesterday_today_dates(self) -> tuple:
        """Get yesterday and today dates"""
        today = datetime.now()
        yesterday = today - timedelta(days=1)
        return yesterday, today

    def _find_or_create_account_row(self, email: str) -> int:
        """Find existing row for account or create new one. Returns row number (1-based)"""
        if not self.service or not self.spreadsheet_id:
            return -1

        try:
            # Read existing data to find the account
            result = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id,
                range='A:A'  # Read all emails in column A
            ).execute()
            
            values = result.get('values', [])
            
            # Check if we have headers
            if not values or values[0][0] != 'Email':
                # Create headers if they don't exist
                self._create_headers()
                return 2  # Return row 2 for first data entry
            
            # Find existing account
            for i, row in enumerate(values[1:], start=2):  # Start from row 2 (skip header)
                if row and row[0] == email:
                    return i
            
            # Account not found, return next available row
            return len(values) + 1
            
        except HttpError as e:
            logger.error(f"Error finding account row: {e}")
            return -1

    def _create_headers(self):
        """Create headers in the spreadsheet"""
        if not self.service or not self.spreadsheet_id:
            return

        yesterday, today = self._get_yesterday_today_dates()
        headers = [
            'Email',
            f'Previous Day ({self._get_date_string(yesterday)})',
            f'Current Day ({self._get_date_string(today)})',
            'Points Earned Today',
            'Last Updated'
        ]

        try:
            self.service.spreadsheets().values().update(
                spreadsheetId=self.spreadsheet_id,
                range='A1:E1',
                valueInputOption='RAW',
                body={'values': [headers]}
            ).execute()
            logger.info("Created headers in Google Sheets")
        except HttpError as e:
            logger.error(f"Error creating headers: {e}")

    def _update_headers_if_needed(self):
        """Update headers if the date has changed"""
        if not self.service or not self.spreadsheet_id:
            return

        try:
            # Read current headers
            result = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id,
                range='A1:E1'
            ).execute()
            
            values = result.get('values', [])
            if not values:
                self._create_headers()
                return

            yesterday, today = self._get_yesterday_today_dates()
            expected_headers = [
                'Email',
                f'Previous Day ({self._get_date_string(yesterday)})',
                f'Current Day ({self._get_date_string(today)})',
                'Points Earned Today',
                'Last Updated'
            ]

            current_headers = values[0] if values else []
            
            # Check if headers need updating (date changed)
            if len(current_headers) < 5 or current_headers[1] != expected_headers[1] or current_headers[2] != expected_headers[2]:
                # Shift data: current day becomes previous day
                self._shift_data_for_new_day()
                # Update headers
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range='A1:E1',
                    valueInputOption='RAW',
                    body={'values': [expected_headers]}
                ).execute()
                logger.info("Updated headers for new day")

        except HttpError as e:
            logger.error(f"Error updating headers: {e}")

    def _shift_data_for_new_day(self):
        """Shift current day data to previous day column for all accounts"""
        if not self.service or not self.spreadsheet_id:
            return

        try:
            # Read all data
            result = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id,
                range='A:E'
            ).execute()
            
            values = result.get('values', [])
            if len(values) <= 1:  # Only headers or no data
                return

            # Prepare batch update
            updates = []
            for i, row in enumerate(values[1:], start=2):  # Skip header row
                if len(row) >= 3:  # Has current day data
                    # Move current day (column C) to previous day (column B)
                    # Clear current day (column C) and points earned (column D)
                    updates.append({
                        'range': f'B{i}:D{i}',
                        'values': [[row[2] if len(row) > 2 else '', '', '']]  # prev_day, current_day, points_earned
                    })

            if updates:
                self.service.spreadsheets().values().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'valueInputOption': 'RAW', 'data': updates}
                ).execute()
                logger.info("Shifted data for new day")

        except HttpError as e:
            logger.error(f"Error shifting data: {e}")

    def sync_account_data(self, email: str, current_points: int, previous_points: int = None):
        """
        Sync account data to Google Sheets
        
        Args:
            email: Account email (password will NOT be logged for security)
            current_points: Current day's reward points
            previous_points: Previous day's reward points (optional)
        """
        if not self.service or not self.spreadsheet_id:
            logger.error("Google Sheets service not available")
            return False

        try:
            # Update headers if needed (handles date changes)
            self._update_headers_if_needed()
            
            # Find or create row for this account
            row_num = self._find_or_create_account_row(email)
            if row_num == -1:
                logger.error(f"Failed to find/create row for account: {email}")
                return False

            # Read existing data for this account
            existing_result = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id,
                range=f'A{row_num}:E{row_num}'
            ).execute()
            
            existing_values = existing_result.get('values', [[]])
            existing_row = existing_values[0] if existing_values else []
            
            # Prepare data
            prev_points = previous_points if previous_points is not None else (
                int(existing_row[1]) if len(existing_row) > 1 and existing_row[1].isdigit() else 0
            )
            
            points_earned = current_points - prev_points
            last_updated = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Prepare row data
            row_data = [
                email,  # A: Email (NO password for security)
                str(prev_points),  # B: Previous day points
                str(current_points),  # C: Current day points
                str(points_earned),  # D: Points earned today
                last_updated  # E: Last updated
            ]

            # Update the row
            self.service.spreadsheets().values().update(
                spreadsheetId=self.spreadsheet_id,
                range=f'A{row_num}:E{row_num}',
                valueInputOption='RAW',
                body={'values': [row_data]}
            ).execute()

            logger.info(f"Successfully synced data for {email}: {prev_points} -> {current_points} (+{points_earned})")
            return True

        except HttpError as e:
            logger.error(f"Error syncing account data: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error syncing account data: {e}")
            return False


# Global instance
_sheets_manager = None

def get_sheets_manager() -> Optional[GoogleSheetsManager]:
    """Get or create Google Sheets manager instance"""
    global _sheets_manager
    if _sheets_manager is None:
        if SPREADSHEET_ID:
            _sheets_manager = GoogleSheetsManager()
        else:
            logger.warning("GOOGLE_SPREADSHEET_ID not set in environment variables")
    return _sheets_manager


def sync_account_to_sheets(email: str, current_points: int, previous_points: int = None) -> bool:
    """
    Convenience function to sync account data to Google Sheets
    
    Args:
        email: Account email
        current_points: Current day's reward points
        previous_points: Previous day's reward points (optional)
    
    Returns:
        bool: True if successful, False otherwise
    """
    manager = get_sheets_manager()
    if manager:
        return manager.sync_account_data(email, current_points, previous_points)
    return False
