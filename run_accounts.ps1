# PowerShell script to automatically run worker.py with JSON-based account management
# Runs continuously until ALL accounts have status "success"

# Check if accounts.json file exists
if (-not (Test-Path "accounts.json")) {
    Write-Host "Error: File accounts.json does not exist!" -ForegroundColor Red
    exit 1
}

# Check if worker.py file exists
if (-not (Test-Path "worker.py")) {
    Write-Host "Error: File worker.py does not exist!" -ForegroundColor Red
    exit 1
}

# Function to check how many accounts have "processing" status
function Get-ProcessingAccountCount {
    try {
        $accounts = Get-Content -Path "accounts.json" -Raw | ConvertFrom-Json
        $processingCount = ($accounts | Where-Object { $_.status -eq "processing" }).Count
        return $processingCount
    }
    catch {
        Write-Host "Error reading accounts.json: $($_.Exception.Message)" -ForegroundColor Red
        return -1
    }
}

# Function to show account status
function Show-AccountStatus {
    try {
        $accounts = Get-Content -Path "accounts.json" -Raw | ConvertFrom-Json
        $totalCount = $accounts.Count
        $successCount = ($accounts | Where-Object { $_.status -eq "success" }).Count
        $processingCount = ($accounts | Where-Object { $_.status -eq "processing" }).Count
        $untriedCount = ($accounts | Where-Object { $_.status -eq "processing" -and -not $_.PSObject.Properties["last_tried"] }).Count
        $triedFailedCount = ($accounts | Where-Object { $_.status -eq "processing" -and $_.PSObject.Properties["last_tried"] }).Count

        Write-Host "=== ACCOUNT STATUS ===" -ForegroundColor Yellow
        Write-Host "Total accounts: $totalCount"
        Write-Host "Completed: $successCount" -ForegroundColor Green
        Write-Host "Pending: $processingCount" -ForegroundColor Cyan
        Write-Host "  - Untried: $untriedCount" -ForegroundColor White
        Write-Host "  - Failed (will retry): $triedFailedCount" -ForegroundColor Yellow
        Write-Host "======================" -ForegroundColor Yellow
    }
    catch {
        Write-Host "Error reading account status: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Starting script with JSON-based account management..." -ForegroundColor Green
Write-Host "Start time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Show-AccountStatus

# Main loop - run until all accounts have status "success"
$round = 1
while ((Get-ProcessingAccountCount) -gt 0) {
    Write-Host ""
    Write-Host "=== ROUND $round ===" -ForegroundColor Magenta
    Write-Host "Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

    # Run worker.py with JSON mode (single-execution mode)
    Write-Host "Running worker.py --json..." -ForegroundColor Cyan
    try {
        python worker.py --json

        if ($LASTEXITCODE -eq 0) {
            Write-Host "Worker.py completed successfully" -ForegroundColor Green
        }
        else {
            Write-Host "Worker.py encountered an error" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "Error running worker.py: $($_.Exception.Message)" -ForegroundColor Red
    }

    # Show current status
    Show-AccountStatus

    # Check if there are still accounts to process
    $remaining = Get-ProcessingAccountCount
    if ($remaining -gt 0) {
        Write-Host "Still $remaining accounts to process. Waiting 2 minutes before continuing..." -ForegroundColor Yellow
        Start-Sleep -Seconds 30
    }

    $round++
}

Write-Host ""
Write-Host "🎉 ALL ACCOUNTS HAVE BEEN PROCESSED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "Completion time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Show-AccountStatus
