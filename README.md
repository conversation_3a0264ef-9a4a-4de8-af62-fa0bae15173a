# edge_rewards
Tool to automate daily Microsoft Rewards tasks using Edge. Searches and browsing to earn points automatically.

## Installation
```bash
pip install -r requirements.txt
```

## Usage

### Single Account Mode
```bash
python main.py --email [email] --password [password]
```

### JSON-Based Account Management (Recommended)
1. Configure your accounts in `accounts.json`:
```json
[
  {
    "email": "<EMAIL>",
    "password": "your-password",
    "reward": 0,
    "status": "processing"
  }
]
```

2. Run the worker:
```bash
python worker.py --json
```

3. Or use the automated scripts:
```bash
# Linux/macOS
./run_accounts.sh

# Windows PowerShell
.\run_accounts.ps1
```

### Options
- `--email`: Your Microsoft account email.
- `--password`: Your Microsoft account password.
- `--times`: The number of times to search and browse, defaults to 35.
- `--headless`: Run the browser in headless mode, defaults to False.
- `--manual`: Skip login, using current logged session, defaults to False.
- `--driver`: The type of chromedriver, options: "chrome", "firefox", "opera", "brave", "edge", defaults to "chrome".
- `--port`: The port of browser debugger, defaults to 9222.
- `--json`: Run in JSON mode (processes one account per execution).
- `--scheduler`: Run as scheduler (runs daily at 01:18).

## Excel Logging

This tool automatically logs reward points to an Excel file (.xlsx) with a rolling 2-day history.

### Excel Features

- **Automatic File Creation**: Creates `rewards_log.xlsx` in the project directory
- **Rolling History**: Maintains 2-day rolling history (yesterday and today)
- **Data Overwrite**: Automatically shifts data when a new day begins
- **Security**: Only logs email addresses (passwords are never logged)
- **Points Tracking**: Tracks previous day points, current day points, and points earned today
- **Status Tracking**: Color-coded status (Green=Success, Orange=Processing, Pink=Failed)
- **Auto-formatting**: Professional formatting with headers and proper column widths

### Excel File Structure

The Excel file contains the following columns:
- **Email**: Account email address
- **Previous Day**: Points from previous day
- **Current Day**: Points from current day
- **Points Earned Today**: Difference between current and previous day
- **Last Updated**: Timestamp of last update
- **Status**: Account processing status (color-coded)


*Note:* If you want to run the script without logging in, open browser in debugger mode, Set `driver` and `port` associated with your browser and run the script with `--manual` option. Make sure you have logged into your Microsoft Rewards account before running the script.