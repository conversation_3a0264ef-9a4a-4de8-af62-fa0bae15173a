# edge_rewards
Too<PERSON> to automate daily Microsoft Rewards tasks using Edge. Searches and browsing to earn points automatically.

## Installation
```bash
pip install -r requirements.txt
```

## Usage

### Single Account Mode
```bash
python main.py --email [email] --password [password]
```

### JSON-Based Account Management (Recommended)
1. Configure your accounts in `accounts.json`:
```json
[
  {
    "email": "<EMAIL>",
    "password": "your-password",
    "reward": 0,
    "status": "processing"
  }
]
```

2. Run the worker:
```bash
python worker.py --json
```

3. Or use the automated scripts:
```bash
# Linux/macOS
./run_accounts.sh

# Windows PowerShell
.\run_accounts.ps1
```

### Options
- `--email`: Your Microsoft account email.
- `--password`: Your Microsoft account password.
- `--times`: The number of times to search and browse, defaults to 35.
- `--headless`: Run the browser in headless mode, defaults to False.
- `--manual`: Skip login, using current logged session, defaults to False.
- `--driver`: The type of chromedriver, options: "chrome", "firefox", "opera", "brave", "edge", defaults to "chrome".
- `--port`: The port of browser debugger, defaults to 9222.
- `--json`: Run in JSON mode (processes one account per execution).
- `--scheduler`: Run as scheduler (runs daily at 01:18).

## Google Sheets Integration

This tool can automatically log reward points to Google Sheets with a rolling 2-day history.

### Setup Google Sheets Integration

1. **Create Google Cloud Project**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one

2. **Enable Google Sheets API**:
   - In the Google Cloud Console, go to "APIs & Services" > "Library"
   - Search for "Google Sheets API" and enable it

3. **Create Credentials**:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client ID"
   - Choose "Desktop Application"
   - Download the credentials file as `credentials.json`

4. **Setup Spreadsheet**:
   - Create a new Google Spreadsheet
   - Copy the spreadsheet ID from the URL (the long string between `/d/` and `/edit`)

5. **Configure Environment**:
   - Copy `.env.example` to `.env`
   - Set `GOOGLE_SPREADSHEET_ID` to your spreadsheet ID
   - Ensure `credentials.json` is in the project root

6. **First Run Authorization**:
   - On first run, the script will open a browser for Google authorization
   - Grant permissions to access your Google Sheets
   - The authorization token will be saved for future use

### Google Sheets Features

- **Automatic Headers**: Creates headers with current and previous day dates
- **Rolling History**: Maintains 2-day rolling history (yesterday and today)
- **Data Overwrite**: Automatically shifts data when a new day begins
- **Security**: Only logs email addresses (passwords are never logged)
- **Points Tracking**: Tracks previous day points, current day points, and points earned today


*Note:* If you want to run the script without logging in, open browser in debugger mode, Set `driver` and `port` associated with your browser and run the script with `--manual` option. Make sure you have logged into your Microsoft Rewards account before running the script.